'use client';

import React, { useState } from 'react';
import { UpcomingFixturesProps } from '../types';
import { LeagueFilter } from './components/LeagueFilter';
import { DateSelector } from './components/DateSelector';
import { FixturesList } from './components/FixturesList';
import { useFixturesData } from './hooks/useFixturesData';

const UpcomingFixturesV2: React.FC<UpcomingFixturesProps> = ({
  className = ''
}) => {
  const [selectedLeague, setSelectedLeague] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0] // Today's date
  );

  // Simplified data fetching
  const {
    fixtures,
    leagues,
    isLoading,
    error,
    refetch
  } = useFixturesData({
    league: selectedLeague,
    date: selectedDate
  });

  return (
    <section className={`py-16 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 ${className}`}>
      <div className="container mx-auto px-4">
        {/* Modern Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold bg-gradient-to-r from-slate-800 to-blue-600 bg-clip-text text-transparent mb-4">
            Fixtures
          </h2>
          <p className="text-slate-600 text-lg max-w-2xl mx-auto">
            Track upcoming matches across all leagues with smart filtering and real-time updates
          </p>
        </div>

        {/* Enhanced Two Column Layout */}
        <div className="grid grid-cols-1 xl:grid-cols-4 lg:grid-cols-1 gap-8 max-w-7xl mx-auto">
          {/* League Filter Sidebar - 25% width */}
          <div className="xl:col-span-1 lg:col-span-1">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 sticky top-6">
              <LeagueFilter
                leagues={leagues}
                selectedLeague={selectedLeague}
                onLeagueChange={setSelectedLeague}
                isLoading={isLoading}
              />
            </div>
          </div>

          {/* Main Content - 75% width */}
          <div className="xl:col-span-3 lg:col-span-1 space-y-6">
            {/* Date Selector */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
              <DateSelector
                selectedDate={selectedDate}
                onDateChange={setSelectedDate}
              />
            </div>

            {/* Fixtures List */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
              <FixturesList
                fixtures={fixtures}
                selectedLeague={selectedLeague}
                selectedDate={selectedDate}
                isLoading={isLoading}
                error={error}
                onRetry={refetch}
              />
            </div>
          </div>
        </div>

        {/* Enhanced Footer */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center space-x-2 text-slate-600 bg-white/60 backdrop-blur-sm px-6 py-3 rounded-full border border-white/30">
            <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
            <span className="text-sm font-medium">Live updates every 30 seconds</span>
          </div>
        </div>
      </div>

    </section>
  );
};

export default UpcomingFixturesV2;
