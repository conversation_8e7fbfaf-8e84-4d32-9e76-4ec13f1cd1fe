/**
 * Image utility functions for V2 components
 * Handles CDN URL construction with fallbacks
 */

/**
 * Get the CDN domain from environment variables with fallback
 */
export const getCDNDomain = (): string => {
  return process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE || 'http://116.203.125.65';
};

/**
 * Construct full CDN URL for an image path
 * @param imagePath - The relative path to the image (e.g., 'public/images/teams/1.png')
 * @returns Full CDN URL or null if no path provided
 */
export const getCDNImageUrl = (imagePath?: string): string | null => {
  if (!imagePath) return null;
  
  const cdnDomain = getCDNDomain();
  
  // Remove leading slash if present to avoid double slashes
  const cleanPath = imagePath.startsWith('/') ? imagePath.slice(1) : imagePath;
  
  return `${cdnDomain}/${cleanPath}`;
};

/**
 * Get team logo URL with fallback handling
 * @param team - Team object with potential logo property
 * @param logoPath - Optional direct logo path
 * @returns CDN URL for team logo or null
 */
export const getTeamLogoUrl = (
  team: { logo?: string }, 
  logoPath?: string
): string | null => {
  const logo = logoPath || team.logo;
  return getCDNImageUrl(logo);
};

/**
 * Get league logo URL
 * @param league - League object with potential logo property
 * @returns CDN URL for league logo or null
 */
export const getLeagueLogoUrl = (league: { logo?: string }): string | null => {
  return getCDNImageUrl(league.logo);
};

/**
 * Check if CDN is available (for future health checks)
 * @returns boolean indicating CDN availability
 */
export const isCDNAvailable = (): boolean => {
  // For now, always return true
  // In the future, this could ping the CDN to check availability
  return true;
};

/**
 * Get fallback initials for missing images
 * @param name - Name to extract initials from
 * @returns First letter of the name in uppercase
 */
export const getImageFallbackInitials = (name: string): string => {
  return name.charAt(0).toUpperCase();
};
