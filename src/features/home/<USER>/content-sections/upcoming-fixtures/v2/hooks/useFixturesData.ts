'use client';

import { useState, useEffect, useCallback } from 'react';

interface Team {
  id: string;
  name: string;
  logo?: string;
}

interface League {
  id: string;
  name: string;
  logo?: string;
  country?: string;
  season?: string;
}

interface Fixture {
  id: string;
  externalId: string;
  slug: string;
  homeTeam: Team;
  awayTeam: Team;
  homeTeamLogo?: string;
  awayTeamLogo?: string;
  kickoffTime: string;
  status: string;
  homeScore?: number;
  awayScore?: number;
  minute?: number;
  isHot?: boolean;
  league?: League;
}

interface UseFixturesDataParams {
  league?: string | null;
  date?: string;
}

interface UseFixturesDataReturn {
  fixtures: Fixture[];
  leagues: League[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useFixturesData = ({
  league,
  date
}: UseFixturesDataParams): UseFixturesDataReturn => {
  const [fixtures, setFixtures] = useState<Fixture[]>([]);
  const [leagues, setLeagues] = useState<League[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch leagues
  const fetchLeagues = useCallback(async () => {
    try {
      const response = await fetch('/api/football/leagues?active=true');
      if (!response.ok) {
        throw new Error('Failed to fetch leagues');
      }
      const data = await response.json();
      setLeagues(data.data || []);
    } catch (err) {
      console.error('Error fetching leagues:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch leagues');
    }
  }, []);

  // Fetch fixtures
  const fetchFixtures = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      if (league) {
        params.append('league', league);
      }
      if (date) {
        params.append('date', date);
      }

      const response = await fetch(`/api/football/fixtures?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch fixtures');
      }
      
      const data = await response.json();
      setFixtures(data.data || []);
    } catch (err) {
      console.error('Error fetching fixtures:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch fixtures');
    } finally {
      setIsLoading(false);
    }
  }, [league, date]);

  // Initial data fetch
  useEffect(() => {
    const loadInitialData = async () => {
      setIsLoading(true);
      await Promise.all([
        fetchLeagues(),
        fetchFixtures()
      ]);
      setIsLoading(false);
    };

    loadInitialData();
  }, []);

  // Refetch fixtures when filters change
  useEffect(() => {
    if (leagues.length > 0) { // Only fetch fixtures after leagues are loaded
      fetchFixtures();
    }
  }, [league, date, fetchFixtures, leagues.length]);

  // Refetch function
  const refetch = useCallback(() => {
    fetchFixtures();
  }, [fetchFixtures]);

  return {
    fixtures,
    leagues,
    isLoading,
    error,
    refetch
  };
};
