import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import UpcomingFixturesV2 from './UpcomingFixturesV2';

// Mock the hook
jest.mock('./hooks/useFixturesData', () => ({
  useFixturesData: jest.fn()
}));

// Mock Next.js components
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: any) {
    return <img src={src} alt={alt} {...props} />;
  };
});

jest.mock('next/link', () => {
  return function MockLink({ href, children, ...props }: any) {
    return <a href={href} {...props}>{children}</a>;
  };
});

const mockUseFixturesData = require('./hooks/useFixturesData').useFixturesData;

const mockLeagues = [
  {
    id: '1',
    name: 'Premier League',
    logo: 'public/images/leagues/1.png',
    country: 'England',
    season: '2024-25'
  },
  {
    id: '2',
    name: 'La Liga',
    logo: 'public/images/leagues/2.png',
    country: 'Spain',
    season: '2024-25'
  }
];

const mockFixtures = [
  {
    id: '1',
    externalId: 'ext1',
    slug: 'premier-league',
    homeTeam: { id: '1', name: 'Arsenal' },
    awayTeam: { id: '2', name: 'Chelsea' },
    homeTeamLogo: 'public/images/teams/1.png',
    awayTeamLogo: 'public/images/teams/2.png',
    kickoffTime: '2024-01-15T15:00:00Z',
    status: 'NS',
    isHot: true,
    league: mockLeagues[0]
  },
  {
    id: '2',
    externalId: 'ext2',
    slug: 'la-liga',
    homeTeam: { id: '3', name: 'Real Madrid' },
    awayTeam: { id: '4', name: 'Barcelona' },
    homeTeamLogo: 'public/images/teams/3.png',
    awayTeamLogo: 'public/images/teams/4.png',
    kickoffTime: '2024-01-15T20:00:00Z',
    status: 'LIVE',
    minute: 45,
    homeScore: 1,
    awayScore: 0,
    league: mockLeagues[1]
  }
];

describe('UpcomingFixturesV2', () => {
  beforeEach(() => {
    mockUseFixturesData.mockReturnValue({
      fixtures: mockFixtures,
      leagues: mockLeagues,
      isLoading: false,
      error: null,
      refetch: jest.fn()
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with fixtures', async () => {
    render(<UpcomingFixturesV2 />);

    // Check header
    expect(screen.getByText('Fixtures')).toBeInTheDocument();
    expect(screen.getByText(/Track upcoming matches/)).toBeInTheDocument();

    // Check leagues section
    expect(screen.getByText('Leagues')).toBeInTheDocument();
    expect(screen.getByText('All Leagues')).toBeInTheDocument();

    // Check date selector
    expect(screen.getByText('Select Date')).toBeInTheDocument();
    expect(screen.getByText('Today')).toBeInTheDocument();
    expect(screen.getByText('Tomorrow')).toBeInTheDocument();

    // Check fixtures
    await waitFor(() => {
      expect(screen.getByText('Arsenal')).toBeInTheDocument();
      expect(screen.getByText('Chelsea')).toBeInTheDocument();
      expect(screen.getByText('Real Madrid')).toBeInTheDocument();
      expect(screen.getByText('Barcelona')).toBeInTheDocument();
    });
  });

  it('shows loading state', () => {
    mockUseFixturesData.mockReturnValue({
      fixtures: [],
      leagues: [],
      isLoading: true,
      error: null,
      refetch: jest.fn()
    });

    render(<UpcomingFixturesV2 />);

    // Should show loading skeletons
    const loadingElements = screen.getAllByRole('generic');
    expect(loadingElements.length).toBeGreaterThan(0);
  });

  it('shows error state', () => {
    const mockRefetch = jest.fn();
    mockUseFixturesData.mockReturnValue({
      fixtures: [],
      leagues: [],
      isLoading: false,
      error: 'Failed to fetch data',
      refetch: mockRefetch
    });

    render(<UpcomingFixturesV2 />);

    expect(screen.getByText('Failed to load fixtures')).toBeInTheDocument();
    expect(screen.getByText('Failed to fetch data')).toBeInTheDocument();
    
    const retryButton = screen.getByText('Try Again');
    fireEvent.click(retryButton);
    expect(mockRefetch).toHaveBeenCalled();
  });

  it('handles league selection', async () => {
    render(<UpcomingFixturesV2 />);

    // Click on a league
    const premierLeagueButton = await screen.findByText('Premier League');
    fireEvent.click(premierLeagueButton);

    // Should call useFixturesData with the selected league
    await waitFor(() => {
      expect(mockUseFixturesData).toHaveBeenCalledWith(
        expect.objectContaining({
          league: '1'
        })
      );
    });
  });

  it('handles date selection', async () => {
    render(<UpcomingFixturesV2 />);

    // Click on Tomorrow
    const tomorrowButton = screen.getByText('Tomorrow');
    fireEvent.click(tomorrowButton);

    // Should update the selected date
    await waitFor(() => {
      expect(mockUseFixturesData).toHaveBeenCalledWith(
        expect.objectContaining({
          date: expect.any(String)
        })
      );
    });
  });

  it('shows hot fixtures with special styling', async () => {
    render(<UpcomingFixturesV2 />);

    // Find the hot fixture (Arsenal vs Chelsea)
    const hotFixture = await screen.findByText('Arsenal');
    const fixtureCard = hotFixture.closest('a');
    
    // Should have hot styling classes
    expect(fixtureCard).toHaveClass('border-red-200');
  });

  it('shows live fixtures with correct status', async () => {
    render(<UpcomingFixturesV2 />);

    // Find the live fixture status
    const liveStatus = await screen.findByText("45'");
    expect(liveStatus).toBeInTheDocument();
    expect(liveStatus).toHaveClass('animate-pulse');
  });

  it('displays fixture scores when available', async () => {
    render(<UpcomingFixturesV2 />);

    // Find the score for the live match
    const score = await screen.findByText('1 - 0');
    expect(score).toBeInTheDocument();
  });

  it('shows empty state when no fixtures', () => {
    mockUseFixturesData.mockReturnValue({
      fixtures: [],
      leagues: mockLeagues,
      isLoading: false,
      error: null,
      refetch: jest.fn()
    });

    render(<UpcomingFixturesV2 />);

    expect(screen.getByText('No fixtures found')).toBeInTheDocument();
    expect(screen.getByText(/No matches scheduled/)).toBeInTheDocument();
  });

  it('toggles custom date picker', () => {
    render(<UpcomingFixturesV2 />);

    // Click custom date toggle
    const customDateButton = screen.getByText('Custom Date');
    fireEvent.click(customDateButton);

    // Should show date input
    const dateInput = screen.getByDisplayValue(expect.any(String));
    expect(dateInput).toBeInTheDocument();

    // Click back to quick select
    const quickSelectButton = screen.getByText('Quick Select');
    fireEvent.click(quickSelectButton);

    // Should show quick options again
    expect(screen.getByText('Today')).toBeInTheDocument();
  });
});
