'use client';

import React, { useState } from 'react';

interface DateSelectorProps {
  selectedDate: string;
  onDateChange: (date: string) => void;
}

export const DateSelector: React.FC<DateSelectorProps> = ({
  selectedDate,
  onDateChange
}) => {
  const [showCustomPicker, setShowCustomPicker] = useState(false);

  // Get quick date options
  const getQuickDates = () => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const dayAfterTomorrow = new Date(today);
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

    return [
      {
        label: 'Today',
        value: today.toISOString().split('T')[0],
        date: today
      },
      {
        label: 'Tomorrow',
        value: tomorrow.toISOString().split('T')[0],
        date: tomorrow
      },
      {
        label: 'Day After',
        value: dayAfterTomorrow.toISOString().split('T')[0],
        date: dayAfterTomorrow
      }
    ];
  };

  const quickDates = getQuickDates();

  const formatDisplayDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (dateString === today.toISOString().split('T')[0]) {
      return 'Today';
    } else if (dateString === tomorrow.toISOString().split('T')[0]) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const formatDateWithDay = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-6 h-6 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">📅</span>
          </div>
          <h3 className="text-lg font-semibold text-slate-800">Select Date</h3>
        </div>
        
        {/* Custom Date Picker Toggle */}
        <button
          onClick={() => setShowCustomPicker(!showCustomPicker)}
          className="text-sm text-indigo-600 hover:text-indigo-700 font-medium transition-colors"
        >
          {showCustomPicker ? 'Quick Select' : 'Custom Date'}
        </button>
      </div>

      {!showCustomPicker ? (
        /* Quick Date Selection */
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
          {quickDates.map((dateOption) => (
            <button
              key={dateOption.value}
              onClick={() => onDateChange(dateOption.value)}
              className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                selectedDate === dateOption.value
                  ? 'border-indigo-500 bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg scale-105'
                  : 'border-slate-200 bg-white hover:border-indigo-300 hover:bg-indigo-50 text-slate-700'
              }`}
            >
              <div className="text-center">
                <div className={`text-lg font-bold ${
                  selectedDate === dateOption.value ? 'text-white' : 'text-slate-800'
                }`}>
                  {dateOption.label}
                </div>
                <div className={`text-sm ${
                  selectedDate === dateOption.value ? 'text-white/80' : 'text-slate-500'
                }`}>
                  {formatDateWithDay(dateOption.date)}
                </div>
              </div>
            </button>
          ))}
        </div>
      ) : (
        /* Custom Date Picker */
        <div className="space-y-4">
          <div className="relative">
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => onDateChange(e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              max={new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
              className="w-full p-4 border-2 border-slate-200 rounded-xl focus:border-indigo-500 focus:ring-0 text-slate-700 bg-white"
            />
          </div>
          
          {/* Date Range Info */}
          <div className="text-center text-sm text-slate-500 bg-slate-50 rounded-lg p-3">
            Select any date from today up to 30 days ahead
          </div>
        </div>
      )}

      {/* Selected Date Display */}
      <div className="bg-gradient-to-r from-slate-50 to-indigo-50 rounded-xl p-4 border border-indigo-100">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-sm text-slate-600 mb-1">Selected Date</div>
            <div className="text-lg font-semibold text-slate-800">
              {formatDisplayDate(selectedDate)}
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-slate-600 mb-1">Day</div>
            <div className="text-lg font-semibold text-indigo-600">
              {new Date(selectedDate).toLocaleDateString('en-US', { weekday: 'long' })}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Navigation */}
      <div className="flex items-center justify-between pt-2">
        <button
          onClick={() => {
            const prevDate = new Date(selectedDate);
            prevDate.setDate(prevDate.getDate() - 1);
            const minDate = new Date().toISOString().split('T')[0];
            if (prevDate.toISOString().split('T')[0] >= minDate) {
              onDateChange(prevDate.toISOString().split('T')[0]);
            }
          }}
          className="flex items-center space-x-2 px-4 py-2 text-slate-600 hover:text-indigo-600 transition-colors"
        >
          <span>←</span>
          <span className="text-sm">Previous</span>
        </button>

        <button
          onClick={() => {
            const nextDate = new Date(selectedDate);
            nextDate.setDate(nextDate.getDate() + 1);
            const maxDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            if (nextDate.toISOString().split('T')[0] <= maxDate) {
              onDateChange(nextDate.toISOString().split('T')[0]);
            }
          }}
          className="flex items-center space-x-2 px-4 py-2 text-slate-600 hover:text-indigo-600 transition-colors"
        >
          <span className="text-sm">Next</span>
          <span>→</span>
        </button>
      </div>
    </div>
  );
};
