'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getTeamLogoUrl, getImageFallbackInitials } from '../utils/imageUtils';

interface Team {
  id: string;
  name: string;
  logo?: string;
}

interface Fixture {
  id: string;
  externalId: string;
  slug: string;
  homeTeam: Team;
  awayTeam: Team;
  homeTeamLogo?: string;
  awayTeamLogo?: string;
  kickoffTime: string;
  status: string;
  homeScore?: number;
  awayScore?: number;
  minute?: number;
  isHot?: boolean;
  league?: {
    id: string;
    name: string;
    logo?: string;
  };
}

interface FixturesListProps {
  fixtures: Fixture[];
  selectedLeague: string | null;
  selectedDate: string;
  isLoading: boolean;
  error: string | null;
  onRetry: () => void;
}

export const FixturesList: React.FC<FixturesListProps> = ({
  fixtures,
  selectedLeague,
  selectedDate,
  isLoading,
  error,
  onRetry
}) => {
  const formatTime = (kickoffTime: string) => {
    const date = new Date(kickoffTime);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatStatus = (fixture: Fixture) => {
    if (fixture.status === 'LIVE' || fixture.status === '1H' || fixture.status === '2H') {
      return {
        text: fixture.minute ? `${fixture.minute}'` : 'LIVE',
        className: 'bg-red-500 text-white animate-pulse'
      };
    } else if (fixture.status === 'FT') {
      return {
        text: 'FT',
        className: 'bg-green-500 text-white'
      };
    } else if (fixture.status === 'HT') {
      return {
        text: 'HT',
        className: 'bg-orange-500 text-white'
      };
    } else {
      return {
        text: formatTime(fixture.kickoffTime),
        className: 'bg-slate-100 text-slate-700'
      };
    }
  };

  const getTeamLogo = (team: Team, logoPath?: string) => {
    const logo = logoPath || team.logo;
    return logo ? `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE}/${logo}` : null;
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-6 h-6 bg-slate-200 rounded-full animate-pulse"></div>
          <div className="h-6 bg-slate-200 rounded w-32 animate-pulse"></div>
        </div>
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-slate-50 rounded-xl p-4 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-slate-200 rounded-full"></div>
                <div className="h-4 bg-slate-200 rounded w-24"></div>
              </div>
              <div className="h-6 bg-slate-200 rounded w-16"></div>
              <div className="flex items-center space-x-3">
                <div className="h-4 bg-slate-200 rounded w-24"></div>
                <div className="w-8 h-8 bg-slate-200 rounded-full"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-red-500 text-2xl">⚠️</span>
        </div>
        <h3 className="text-lg font-semibold text-slate-800 mb-2">Failed to load fixtures</h3>
        <p className="text-slate-600 mb-6">{error}</p>
        <button
          onClick={onRetry}
          className="px-6 py-3 bg-indigo-500 text-white rounded-xl hover:bg-indigo-600 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (fixtures.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-slate-400 text-2xl">📅</span>
        </div>
        <h3 className="text-lg font-semibold text-slate-800 mb-2">No fixtures found</h3>
        <p className="text-slate-600">
          No matches scheduled for the selected date
          {selectedLeague && ' and league'}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">⚽</span>
          </div>
          <h3 className="text-lg font-semibold text-slate-800">
            Fixtures ({fixtures.length})
          </h3>
        </div>

        <div className="text-sm text-slate-500">
          {new Date(selectedDate).toLocaleDateString('en-US', {
            weekday: 'long',
            month: 'long',
            day: 'numeric'
          })}
        </div>
      </div>

      {/* Fixtures List */}
      <div className="space-y-3 max-h-96 overflow-y-auto custom-scrollbar">
        {fixtures.map((fixture) => {
          const status = formatStatus(fixture);
          const homeLogoUrl = getTeamLogo(fixture.homeTeam, fixture.homeTeamLogo);
          const awayLogoUrl = getTeamLogo(fixture.awayTeam, fixture.awayTeamLogo);

          return (
            <Link
              key={fixture.id}
              href={`/${fixture.slug}/${fixture.externalId}`}
              className="block group"
            >
              <div className={`relative bg-white rounded-xl border-2 transition-all duration-200 p-4 hover:shadow-lg hover:scale-102 ${fixture.isHot
                ? 'border-red-200 bg-gradient-to-r from-red-50 to-orange-50'
                : 'border-slate-200 hover:border-indigo-300'
                }`}>
                {/* Hot Match Indicator */}
                {fixture.isHot && (
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
                    <span className="text-white text-xs font-bold">🔥</span>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  {/* Home Team */}
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <div className="w-10 h-10 rounded-full overflow-hidden bg-slate-100 flex-shrink-0">
                      {homeLogoUrl ? (
                        <Image
                          src={homeLogoUrl}
                          alt={fixture.homeTeam.name}
                          width={40}
                          height={40}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-slate-200 to-slate-300 flex items-center justify-center">
                          <span className="text-slate-600 text-sm font-bold">
                            {fixture.homeTeam.name.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="font-medium text-slate-800 truncate">
                        {fixture.homeTeam.name}
                      </div>
                      {fixture.league && (
                        <div className="text-xs text-slate-500 truncate">
                          {fixture.league.name}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Score/Status */}
                  <div className="flex flex-col items-center space-y-2 px-4">
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${status.className}`}>
                      {status.text}
                    </div>
                    {(fixture.homeScore !== undefined && fixture.awayScore !== undefined) && (
                      <div className="text-lg font-bold text-slate-800">
                        {fixture.homeScore} - {fixture.awayScore}
                      </div>
                    )}
                  </div>

                  {/* Away Team */}
                  <div className="flex items-center space-x-3 flex-1 min-w-0 justify-end">
                    <div className="min-w-0 flex-1 text-right">
                      <div className="font-medium text-slate-800 truncate">
                        {fixture.awayTeam.name}
                      </div>
                      {fixture.league && (
                        <div className="text-xs text-slate-500 truncate">
                          {fixture.league.name}
                        </div>
                      )}
                    </div>
                    <div className="w-10 h-10 rounded-full overflow-hidden bg-slate-100 flex-shrink-0">
                      {awayLogoUrl ? (
                        <Image
                          src={awayLogoUrl}
                          alt={fixture.awayTeam.name}
                          width={40}
                          height={40}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-slate-200 to-slate-300 flex items-center justify-center">
                          <span className="text-slate-600 text-sm font-bold">
                            {fixture.awayTeam.name.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          );
        })}
      </div>
    </div>
  );
};
