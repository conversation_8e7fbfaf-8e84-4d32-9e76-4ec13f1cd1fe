'use client';

import React from 'react';
import Image from 'next/image';

interface League {
  id: string;
  name: string;
  logo?: string;
  country?: string;
  season?: string;
}

interface LeagueFilterProps {
  leagues: League[];
  selectedLeague: string | null;
  onLeagueChange: (leagueId: string | null) => void;
  isLoading: boolean;
}

export const LeagueFilter: React.FC<LeagueFilterProps> = ({
  leagues,
  selectedLeague,
  onLeagueChange,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-6 h-6 bg-slate-200 rounded-full animate-pulse"></div>
          <div className="h-6 bg-slate-200 rounded w-24 animate-pulse"></div>
        </div>
        {[...Array(6)].map((_, i) => (
          <div key={i} className="flex items-center space-x-3 p-3">
            <div className="w-8 h-8 bg-slate-200 rounded-full animate-pulse"></div>
            <div className="flex-1">
              <div className="h-4 bg-slate-200 rounded w-3/4 animate-pulse mb-1"></div>
              <div className="h-3 bg-slate-200 rounded w-1/2 animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
          <span className="text-white text-xs font-bold">⚽</span>
        </div>
        <h3 className="text-lg font-semibold text-slate-800">Leagues</h3>
      </div>

      {/* All Leagues Option */}
      <button
        onClick={() => onLeagueChange(null)}
        className={`w-full flex items-center space-x-3 p-3 rounded-xl transition-all duration-200 ${
          selectedLeague === null
            ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg scale-105'
            : 'bg-slate-50 hover:bg-slate-100 text-slate-700'
        }`}
      >
        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
          selectedLeague === null ? 'bg-white/20' : 'bg-slate-200'
        }`}>
          <span className={`text-sm font-bold ${
            selectedLeague === null ? 'text-white' : 'text-slate-600'
          }`}>
            ALL
          </span>
        </div>
        <div className="flex-1 text-left">
          <div className="font-medium">All Leagues</div>
          <div className={`text-sm ${
            selectedLeague === null ? 'text-white/80' : 'text-slate-500'
          }`}>
            View all fixtures
          </div>
        </div>
      </button>

      {/* League List */}
      <div className="space-y-2 max-h-96 overflow-y-auto custom-scrollbar">
        {leagues.map((league) => (
          <button
            key={league.id}
            onClick={() => onLeagueChange(league.id)}
            className={`w-full flex items-center space-x-3 p-3 rounded-xl transition-all duration-200 ${
              selectedLeague === league.id
                ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg scale-105'
                : 'bg-slate-50 hover:bg-slate-100 text-slate-700 hover:scale-102'
            }`}
          >
            {/* League Logo */}
            <div className="w-8 h-8 rounded-full overflow-hidden bg-white flex-shrink-0">
              {league.logo ? (
                <Image
                  src={`${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE}${league.logo}`}
                  alt={league.name}
                  width={32}
                  height={32}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-slate-200 to-slate-300 flex items-center justify-center">
                  <span className="text-slate-600 text-xs font-bold">
                    {league.name.charAt(0)}
                  </span>
                </div>
              )}
            </div>

            {/* League Info */}
            <div className="flex-1 text-left min-w-0">
              <div className="font-medium truncate">{league.name}</div>
              {league.country && (
                <div className={`text-sm truncate ${
                  selectedLeague === league.id ? 'text-white/80' : 'text-slate-500'
                }`}>
                  {league.country} {league.season && `• ${league.season}`}
                </div>
              )}
            </div>

            {/* Selection Indicator */}
            {selectedLeague === league.id && (
              <div className="w-2 h-2 bg-white rounded-full flex-shrink-0"></div>
            )}
          </button>
        ))}
      </div>

      {/* League Count */}
      <div className="text-center pt-4 border-t border-slate-200">
        <span className="text-sm text-slate-500">
          {leagues.length} leagues available
        </span>
      </div>
    </div>
  );
};
